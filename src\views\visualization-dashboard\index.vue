<template>
  <div class="visualization-dashboard">
    <!-- 全屏按钮 -->
    <div class="fullscreen-button" @click="openFullscreen">
      <svg
        class="fullscreen-icon"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7 14H5v5h5v-2M7 10H5V5h5v2m10 5v5h-5v-2m0-8h5V5h-5v2"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <span class="fullscreen-text">全屏显示</span>
    </div>

    <iframe src="http://*************:8084/daping/#/" class="fullscreen-iframe"></iframe>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'VisualizationDashboard',
    setup() {
      // 打开全屏新标签页
      const openFullscreen = () => {
        window.open('http://*************:8084/daping/#/', '_blank')
      }

      return {
        openFullscreen
      }
    }
  })
</script>

<style lang="scss" scoped>
  .visualization-dashboard {
    position: relative; // 为绝对定位的按钮提供参考点
    display: flex; // 设置为flex容器
    flex-direction: column; // 垂直排列子元素
    width: 100%;
    height: 100%;
    overflow: hidden; // 防止 iframe 可能产生的滚动条影响外层布局
  }

  .fullscreen-button {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(0 0 0 / 10%);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;

    &:hover {
      background: rgb(255 255 255 / 100%);
      box-shadow: 0 6px 20px rgb(0 0 0 / 15%);
      transform: translateY(-2px);
    }

    &:active {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      transform: translateY(0);
    }
  }

  .fullscreen-icon {
    width: 18px;
    height: 18px;
    color: #1890ff;
    transition: color 0.3s ease;

    .fullscreen-button:hover & {
      color: #096dd9;
    }
  }

  .fullscreen-text {
    font-size: 14px;
    font-weight: 500;
    color: #1890ff;
    white-space: nowrap;
    transition: color 0.3s ease;

    .fullscreen-button:hover & {
      color: #096dd9;
    }
  }

  .fullscreen-iframe {
    flex-grow: 1; // iframe 在flex容器中占据所有可用空间
    width: 100%;
    border: none; // 移除 iframe 的默认边框
    // height: 100%; // 在flex-grow:1 的情况下，如果父元素有确定高度，此行可省略，但保留也无害
  }
</style>
